import { Position } from '@xyflow/react'
import { useEditorStore } from '../editorStore'

// Mock the zustand store
jest.mock('zustand', () => ({
  create: (fn: any) => {
    const store = fn(() => ({}), () => ({}), {})
    return () => store
  },
}))

describe('EditorStore - toggleNodePorts', () => {
  let store: ReturnType<typeof useEditorStore>

  beforeEach(() => {
    // Reset the store before each test
    store = useEditorStore.getState()
    
    // Set up initial test data
    const initialNodes = [
      {
        id: 'node1',
        position: { x: 0, y: 0 },
        data: { 
          label: 'Node 1',
          hasVerticalPorts: false // horizontal orientation
        },
        type: 'custom'
      },
      {
        id: 'node2', 
        position: { x: 200, y: 0 },
        data: { 
          label: 'Node 2',
          hasVerticalPorts: true // vertical orientation
        },
        type: 'custom'
      }
    ]

    const initialEdges = [
      {
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        type: 'shoshin',
        animated: true
      }
    ]

    store.setNodes(initialNodes)
    store.setEdges(initialEdges)
  })

  it('should toggle node orientation from horizontal to vertical', () => {
    const initialNode = store.nodes.find(n => n.id === 'node1')
    expect(initialNode?.data?.hasVerticalPorts).toBe(false)

    // Toggle the node orientation
    store.toggleNodePorts('node1')

    const updatedNode = store.nodes.find(n => n.id === 'node1')
    expect(updatedNode?.data?.hasVerticalPorts).toBe(true)
  })

  it('should toggle node orientation from vertical to horizontal', () => {
    const initialNode = store.nodes.find(n => n.id === 'node2')
    expect(initialNode?.data?.hasVerticalPorts).toBe(true)

    // Toggle the node orientation
    store.toggleNodePorts('node2')

    const updatedNode = store.nodes.find(n => n.id === 'node2')
    expect(updatedNode?.data?.hasVerticalPorts).toBe(false)
  })

  it('should update connected edges when source node orientation changes', () => {
    // Initial state: node1 (horizontal) -> node2 (vertical)
    const initialEdge = store.edges.find(e => e.id === 'edge1')
    
    // Toggle node1 from horizontal to vertical
    store.toggleNodePorts('node1')

    const updatedEdge = store.edges.find(e => e.id === 'edge1') as any
    
    // Source node (node1) is now vertical, so source position should be Bottom
    expect(updatedEdge.sourcePosition).toBe(Position.Bottom)
  })

  it('should update connected edges when target node orientation changes', () => {
    // Initial state: node1 (horizontal) -> node2 (vertical)
    const initialEdge = store.edges.find(e => e.id === 'edge1')
    
    // Toggle node2 from vertical to horizontal
    store.toggleNodePorts('node2')

    const updatedEdge = store.edges.find(e => e.id === 'edge1') as any
    
    // Target node (node2) is now horizontal, so target position should be Left
    expect(updatedEdge.targetPosition).toBe(Position.Left)
  })

  it('should handle multiple edges connected to the same node', () => {
    // Add another edge connected to node1
    const additionalEdge = {
      id: 'edge2',
      source: 'node1',
      target: 'node2',
      type: 'shoshin',
      animated: true
    }
    
    store.setEdges([...store.edges, additionalEdge])

    // Toggle node1 orientation
    store.toggleNodePorts('node1')

    const updatedEdges = store.edges.filter(e => e.source === 'node1') as any[]
    
    // Both edges should have updated source positions
    updatedEdges.forEach(edge => {
      expect(edge.sourcePosition).toBe(Position.Bottom)
    })
  })

  it('should not affect edges when toggling a node with no connections', () => {
    // Add a disconnected node
    const disconnectedNode = {
      id: 'node3',
      position: { x: 400, y: 0 },
      data: { 
        label: 'Node 3',
        hasVerticalPorts: false
      },
      type: 'custom'
    }
    
    store.setNodes([...store.nodes, disconnectedNode])
    const initialEdgesCount = store.edges.length

    // Toggle the disconnected node
    store.toggleNodePorts('node3')

    // Edges should remain unchanged
    expect(store.edges.length).toBe(initialEdgesCount)
    
    // But the node should be updated
    const updatedNode = store.nodes.find(n => n.id === 'node3')
    expect(updatedNode?.data?.hasVerticalPorts).toBe(true)
  })

  it('should handle non-existent node gracefully', () => {
    const initialNodesCount = store.nodes.length
    const initialEdgesCount = store.edges.length

    // Try to toggle a non-existent node
    store.toggleNodePorts('non-existent-node')

    // Nothing should change
    expect(store.nodes.length).toBe(initialNodesCount)
    expect(store.edges.length).toBe(initialEdgesCount)
  })
})
