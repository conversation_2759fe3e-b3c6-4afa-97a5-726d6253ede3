"use client";

import type { Edge, Node } from "@xyflow/react";
import { addEdge, Position } from "@xyflow/react";
import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

interface HistoryState {
  nodes: Node[];
  edges: Edge[];
  timestamp: number;
}

interface EditorState {
  // Current state
  nodes: Node[];
  edges: Edge[];

  // History
  undoStack: HistoryState[];
  redoStack: HistoryState[];

  // Selection
  selectedNodes: Node[];
  selectedEdges: Edge[];

  // Flow highlighting
  highlightedNodes: string[];
  highlightedEdges: string[];

  // Clipboard
  clipboardData: {
    nodes: Node[];
    edges: Edge[];
    timestamp: number;
  } | null;

  // Cursor position
  lastCursorPosition: { x: number; y: number };

  // Modal state
  nodeSettingsModal: {
    open: boolean;
    nodeId: string | null;
  };

  // Actions
  setNodes: (nodes: Node[]) => void;
  setEdges: (edges: Edge[]) => void;
  updateSelection: (nodes: Node[], edges: Edge[]) => void;

  // Flow highlighting actions
  highlightFlow: (nodeId: string) => void;
  clearHighlight: () => void;

  // History actions
  saveState: () => void;
  undo: () => boolean;
  redo: () => boolean;
  canUndo: () => boolean;
  canRedo: () => boolean;

  // Clipboard actions
  copy: () => void;
  cut: () => void;
  paste: (position?: { x: number; y: number }) => void;

  // Cursor position actions
  updateCursorPosition: (position: { x: number; y: number }) => void;

  // Node/Edge operations
  addNode: (node: Node) => void;
  deleteSelected: () => void;
  deleteEdge: (edgeId: string) => void;
  connectNodes: (connection: any) => void;

  // Node action operations
  toggleNodeEnabled: (nodeId: string) => void;
  duplicateNode: (nodeId: string) => void;
  toggleNodePorts: (nodeId: string) => void;
  deleteNode: (nodeId: string) => void;
  openNodeSettings: (nodeId: string) => void;

  // Modal actions
  setNodeSettingsModal: (open: boolean, nodeId?: string | null) => void;
}

const MAX_HISTORY_SIZE = 50;

const initialNodes: Node[] = [
  {
    id: "start-1",
    type: "custom",
    position: { x: 250, y: 100 },
    data: {
      label: "Start",
      type: "start",
      description: "Start Workflow",
    },
  },
  {
    id: "llm-1",
    type: "custom",
    position: { x: 400, y: 200 },
    data: {
      label: "LLM",
      type: "agent",
      description: "Language Model",
    },
  },
  {
    id: "knowledge-1",
    type: "custom",
    position: { x: 600, y: 150 },
    data: {
      label: "Knowledge",
      type: "knowledge",
      description: "Vector Search",
    },
  },
  {
    id: "response-1",
    type: "custom",
    position: { x: 800, y: 100 },
    data: {
      label: "Response",
      type: "response",
      description: "API Response",
    },
  },
  {
    id: "condition-1",
    type: "custom",
    position: { x: 500, y: 300 },
    data: {
      label: "Condition",
      type: "condition",
      description: "Decision Point",
    },
  },
  {
    id: "function-1",
    type: "custom",
    position: { x: 700, y: 250 },
    data: {
      label: "Function",
      type: "function",
      description: "Process Data",
    },
  },
];

const initialEdges: Edge[] = [
  {
    id: "e1-2",
    source: "start-1",
    target: "llm-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "e2-3",
    source: "llm-1",
    target: "knowledge-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "e3-4",
    source: "knowledge-1",
    target: "response-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "start-condition",
    source: "start-1",
    target: "condition-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "condition-function",
    source: "condition-1",
    target: "function-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "function-response",
    source: "function-1",
    target: "response-1",
    type: "shoshin",
    animated: true,
  },
];

const generateUniqueId = (prefix: string): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// Helper function to find ALL paths from start to target node using DFS
function findAllPaths(
  startId: string,
  targetId: string,
  graph: Map<string, { nodeId: string; edgeId: string }[]>,
  visited: Set<string> = new Set(),
  currentPath: string[] = [],
  currentEdges: string[] = [],
): {
  highlightedNodes: string[];
  highlightedEdges: string[];
}[] {
  if (startId === targetId) {
    return [
      {
        highlightedNodes: [...currentPath, startId],
        highlightedEdges: [...currentEdges],
      },
    ];
  }

  if (visited.has(startId)) {
    return [];
  }

  visited.add(startId);
  const allPaths: {
    highlightedNodes: string[];
    highlightedEdges: string[];
  }[] = [];

  const neighbors = graph.get(startId) || [];
  for (const { nodeId: nextNodeId, edgeId } of neighbors) {
    const paths = findAllPaths(
      nextNodeId,
      targetId,
      graph,
      new Set(visited),
      [...currentPath, startId],
      [...currentEdges, edgeId],
    );
    allPaths.push(...paths);
  }

  return allPaths;
}

// Function to build linear flow from start nodes to target node
function buildLinearFlow(
  targetNodeId: string,
  nodes: Node[],
  edges: Edge[],
): {
  highlightedNodes: string[];
  highlightedEdges: string[];
} {
  // Find start nodes
  const startNodes = nodes.filter((node) => node.data?.type === "start");

  if (startNodes.length === 0) {
    return { highlightedNodes: [], highlightedEdges: [] };
  }

  // Build adjacency list from edges
  const graph = new Map<string, { nodeId: string; edgeId: string }[]>();
  edges.forEach((edge) => {
    if (!graph.has(edge.source)) {
      graph.set(edge.source, []);
    }
    graph.get(edge.source)!.push({ nodeId: edge.target, edgeId: edge.id });
  });

  // Find ALL paths from each start node to target
  const allHighlightedNodes = new Set<string>();
  const allHighlightedEdges = new Set<string>();

  for (const startNode of startNodes) {
    const paths = findAllPaths(startNode.id, targetNodeId, graph);

    // Add all nodes and edges from all paths to the sets
    paths.forEach((path) => {
      path.highlightedNodes.forEach((nodeId) =>
        allHighlightedNodes.add(nodeId),
      );
      path.highlightedEdges.forEach((edgeId) =>
        allHighlightedEdges.add(edgeId),
      );
    });
  }

  return {
    highlightedNodes: Array.from(allHighlightedNodes),
    highlightedEdges: Array.from(allHighlightedEdges),
  };
}

export const useEditorStore = create<EditorState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    nodes: initialNodes,
    edges: initialEdges,
    undoStack: [],
    redoStack: [],
    selectedNodes: [],
    selectedEdges: [],
    highlightedNodes: [],
    highlightedEdges: [],
    clipboardData: null,
    lastCursorPosition: { x: 400, y: 300 }, // Better default position (roughly center of typical viewport)
    nodeSettingsModal: {
      open: false,
      nodeId: null,
    },

    // Basic setters
    setNodes: (nodes) => set({ nodes }),
    setEdges: (edges) => set({ edges }),

    updateSelection: (nodes, edges) => {
      const selectedNodes = nodes.filter((node) => node.selected);
      const selectedEdges = edges.filter((edge) => edge.selected);
      set({ selectedNodes, selectedEdges });

      // Trigger flow highlighting when a single node is selected
      if (selectedNodes.length === 1) {
        const { highlightFlow } = get();
        highlightFlow(selectedNodes[0].id);
      } else {
        // Clear highlighting when no nodes or multiple nodes are selected
        const { clearHighlight } = get();
        clearHighlight();
      }
    },

    // History management
    saveState: () => {
      const { nodes, edges, undoStack } = get();
      const newState: HistoryState = {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
        timestamp: Date.now(),
      };

      const newUndoStack = [...undoStack, newState];
      if (newUndoStack.length > MAX_HISTORY_SIZE) {
        newUndoStack.shift();
      }

      set({
        undoStack: newUndoStack,
        redoStack: [], // Clear redo stack when new action is performed
      });
    },

    undo: () => {
      const { undoStack, nodes, edges } = get();
      if (undoStack.length === 0) return false;

      const previousState = undoStack[undoStack.length - 1];
      const newUndoStack = undoStack.slice(0, -1);

      // Save current state to redo stack
      const currentState: HistoryState = {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
        timestamp: Date.now(),
      };

      set({
        nodes: previousState.nodes,
        edges: previousState.edges,
        undoStack: newUndoStack,
        redoStack: [...get().redoStack, currentState],
      });

      return true;
    },

    redo: () => {
      const { redoStack, nodes, edges } = get();
      if (redoStack.length === 0) return false;

      const nextState = redoStack[redoStack.length - 1];
      const newRedoStack = redoStack.slice(0, -1);

      // Save current state to undo stack
      const currentState: HistoryState = {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
        timestamp: Date.now(),
      };

      set({
        nodes: nextState.nodes,
        edges: nextState.edges,
        undoStack: [...get().undoStack, currentState],
        redoStack: newRedoStack,
      });

      return true;
    },

    canUndo: () => get().undoStack.length > 0,
    canRedo: () => get().redoStack.length > 0,

    // Clipboard operations
    copy: () => {
      const { selectedNodes, selectedEdges } = get();
      if (selectedNodes.length === 0 && selectedEdges.length === 0) return;

      // Get all edges that connect the selected nodes
      const nodeIds = new Set(selectedNodes.map((node) => node.id));
      const relevantEdges = selectedEdges.filter(
        (edge) => nodeIds.has(edge.source) && nodeIds.has(edge.target),
      );

      set({
        clipboardData: {
          nodes: JSON.parse(JSON.stringify(selectedNodes)),
          edges: JSON.parse(JSON.stringify(relevantEdges)),
          timestamp: Date.now(),
        },
      });
    },

    cut: () => {
      const { copy, deleteSelected } = get();
      copy();
      deleteSelected();
    },

    paste: (position) => {
      const { clipboardData, nodes, edges, saveState, lastCursorPosition } =
        get();
      if (!clipboardData || clipboardData.nodes.length === 0) {
        console.log("❌ Paste failed: No clipboard data");
        return;
      }

      saveState();

      // Use provided position, or last cursor position, or a smart default
      let pastePosition = position || lastCursorPosition;

      // If the cursor position is still the default, try to find a better position
      if (
        !position &&
        lastCursorPosition.x === 400 &&
        lastCursorPosition.y === 300
      ) {
        // Find an empty area to paste nodes
        const existingPositions = nodes.map((node) => node.position);
        let offsetX = 50;
        let offsetY = 50;

        // Find a position that doesn't overlap with existing nodes
        while (
          existingPositions.some(
            (pos) =>
              Math.abs(pos.x - (pastePosition.x + offsetX)) < 100 &&
              Math.abs(pos.y - (pastePosition.y + offsetY)) < 100,
          )
        ) {
          offsetX += 150;
          if (offsetX > 600) {
            offsetX = 50;
            offsetY += 150;
          }
        }

        pastePosition = {
          x: pastePosition.x + offsetX,
          y: pastePosition.y + offsetY,
        };
      }

      console.log("🎯 Paste operation:", {
        providedPosition: position,
        lastCursorPosition,
        finalPosition: pastePosition,
        clipboardNodesCount: clipboardData.nodes.length,
        viewport: (window as any).reactFlowInstance?.getViewport?.(),
      });

      // Find the top-left position of copied nodes to calculate offset
      const minX = Math.min(
        ...clipboardData.nodes.map((node) => node.position.x),
      );
      const minY = Math.min(
        ...clipboardData.nodes.map((node) => node.position.y),
      );

      const offsetX = pastePosition.x - minX;
      const offsetY = pastePosition.y - minY;

      // Create mapping from old IDs to new IDs
      const idMapping = new Map<string, string>();

      // Create new nodes with unique IDs and adjusted positions
      const newNodes: Node[] = clipboardData.nodes.map((node) => {
        const nodeType =
          typeof node.data?.type === "string" ? node.data.type : "node";
        const newId = generateUniqueId(nodeType);
        idMapping.set(node.id, newId);

        return {
          ...node,
          id: newId,
          position: {
            x: node.position.x + offsetX,
            y: node.position.y + offsetY,
          },
          selected: false,
        };
      });

      // Create new edges with updated node references
      const newEdges: Edge[] = clipboardData.edges
        .filter(
          (edge) => idMapping.has(edge.source) && idMapping.has(edge.target),
        )
        .map((edge) => ({
          ...edge,
          id: generateUniqueId("edge"),
          source: idMapping.get(edge.source)!,
          target: idMapping.get(edge.target)!,
          selected: false,
        }));

      // Helper function to find the closest node to a given position (same as in MainCanvas)
      const findClosestNode = (newNodePosition: {
        x: number;
        y: number;
      }): Node | null => {
        const MAX_AUTO_CONNECT_DISTANCE = 800; // Maximum distance for auto-connection

        const existingNodes = nodes
          .map((node) => ({
            ...node,
            distance: Math.sqrt(
              (node.position.x - newNodePosition.x) ** 2 +
                (node.position.y - newNodePosition.y) ** 2,
            ),
          }))
          .filter((node) => node.distance <= MAX_AUTO_CONNECT_DISTANCE) // Only consider nodes within reasonable distance
          .sort((a, b) => a.distance - b.distance);

        return existingNodes[0] || null;
      };

      // Auto-connect pasted nodes to nearest existing nodes
      const autoConnectEdges: Edge[] = [];

      // Only auto-connect if there are existing nodes and we're pasting a single node
      if (nodes.length > 0 && newNodes.length === 1) {
        const pastedNode = newNodes[0];
        const closestNode = findClosestNode(pastedNode.position);

        if (closestNode) {
          console.log(
            `🔗 Auto-connecting pasted node "${pastedNode.data.label}" to closest node "${closestNode.data.label}"`,
          );

          // Create an edge from the closest node's exit point to the pasted node's entry point
          const autoConnectEdge: Edge = {
            id: generateUniqueId("edge"),
            source: closestNode.id,
            target: pastedNode.id,
            type: "shoshin",
            animated: true,
            selected: false,
          };

          autoConnectEdges.push(autoConnectEdge);
        } else {
          console.log(
            `ℹ️ No suitable node found for auto-connection (all nodes too far from pasted position)`,
          );
        }
      } else if (nodes.length === 0) {
        console.log(`ℹ️ No existing nodes to connect pasted node to`);
      } else if (newNodes.length > 1) {
        console.log(
          `ℹ️ Auto-connection skipped for multiple pasted nodes (${newNodes.length} nodes)`,
        );
      }

      set({
        nodes: [...nodes, ...newNodes],
        edges: [...edges, ...newEdges, ...autoConnectEdges],
      });
    },

    // Cursor position actions
    updateCursorPosition: (position) => {
      set({ lastCursorPosition: position });
    },

    // Node/Edge operations
    addNode: (node) => {
      const { saveState, nodes } = get();
      saveState();
      set({ nodes: [...nodes, node] });
    },

    deleteSelected: () => {
      const { selectedNodes, selectedEdges, nodes, edges, saveState } = get();
      if (selectedNodes.length === 0 && selectedEdges.length === 0) return;

      saveState();

      const selectedNodeIds = selectedNodes.map((node) => node.id);
      const selectedEdgeIds = selectedEdges.map((edge) => edge.id);

      // Remove selected nodes and edges
      const newNodes = nodes.filter(
        (node) => !selectedNodeIds.includes(node.id),
      );
      const newEdges = edges.filter(
        (edge) =>
          !selectedEdgeIds.includes(edge.id) &&
          !selectedNodeIds.includes(edge.source) &&
          !selectedNodeIds.includes(edge.target),
      );

      set({
        nodes: newNodes,
        edges: newEdges,
        selectedNodes: [],
        selectedEdges: [],
      });
    },

    deleteEdge: (edgeId) => {
      const { edges, saveState } = get();
      saveState();

      const newEdges = edges.filter((edge) => edge.id !== edgeId);
      set({
        edges: newEdges,
        selectedEdges: [],
      });
    },

    // Flow highlighting functions
    highlightFlow: (nodeId) => {
      const { nodes, edges } = get();
      const result = buildLinearFlow(nodeId, nodes, edges);
      set({
        highlightedNodes: result.highlightedNodes,
        highlightedEdges: result.highlightedEdges,
      });
    },

    clearHighlight: () => {
      set({
        highlightedNodes: [],
        highlightedEdges: [],
      });
    },

    connectNodes: (connection) => {
      const { saveState, edges } = get();
      saveState();
      set({ edges: addEdge(connection, edges) });
    },

    // Node action operations
    toggleNodeEnabled: (nodeId) => {
      const { nodes, saveState } = get();
      saveState();

      const updatedNodes = nodes.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              enabled: !(node.data?.enabled ?? true),
            },
          };
        }
        return node;
      });

      set({ nodes: updatedNodes });
    },

    duplicateNode: (nodeId) => {
      const { nodes, saveState } = get();
      const nodeToDuplicate = nodes.find((node) => node.id === nodeId);
      if (!nodeToDuplicate) return;

      saveState();

      // Generate unique ID for the duplicated node
      const newNodeId = `${nodeToDuplicate.data?.type || "node"}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Create duplicated node with offset position
      const duplicatedNode: Node = {
        ...nodeToDuplicate,
        id: newNodeId,
        position: {
          x: nodeToDuplicate.position.x + 50,
          y: nodeToDuplicate.position.y + 50,
        },
        selected: false,
      };

      set({ nodes: [...nodes, duplicatedNode] });
    },

    toggleNodePorts: (nodeId) => {
      const { nodes, edges, saveState } = get();
      saveState();

      // Find the node being toggled to get its new orientation
      const targetNode = nodes.find((node) => node.id === nodeId);
      if (!targetNode) return;

      const newHasVerticalPorts = !(targetNode.data?.hasVerticalPorts ?? false);

      // Update the node with new orientation
      const updatedNodes = nodes.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              hasVerticalPorts: newHasVerticalPorts,
            },
          };
        }
        return node;
      });

      // Helper function to get the correct handle positions based on node orientation
      const getHandlePositions = (node: Node, isSource: boolean) => {
        const hasVerticalPorts = node.data?.hasVerticalPorts ?? false;
        if (hasVerticalPorts) {
          return isSource ? Position.Bottom : Position.Top;
        } else {
          return isSource ? Position.Right : Position.Left;
        }
      };

      // Update connected edges to reflect new handle positions
      const updatedEdges = edges.map((edge) => {
        let updatedEdge = { ...edge } as any;

        // If this node is the source of the edge, update sourcePosition
        if (edge.source === nodeId) {
          const sourceNode = updatedNodes.find((n) => n.id === nodeId);
          if (sourceNode) {
            updatedEdge.sourcePosition = getHandlePositions(sourceNode, true);
          }
        }

        // If this node is the target of the edge, update targetPosition
        if (edge.target === nodeId) {
          const targetNode = updatedNodes.find((n) => n.id === nodeId);
          if (targetNode) {
            updatedEdge.targetPosition = getHandlePositions(targetNode, false);
          }
        }

        return updatedEdge;
      });

      set({ nodes: updatedNodes, edges: updatedEdges });
    },

    deleteNode: (nodeId) => {
      const { nodes, edges, saveState } = get();
      saveState();

      // Remove the node
      const newNodes = nodes.filter((node) => node.id !== nodeId);

      // Remove all edges connected to this node
      const newEdges = edges.filter(
        (edge) => edge.source !== nodeId && edge.target !== nodeId,
      );

      set({
        nodes: newNodes,
        edges: newEdges,
        selectedNodes: [],
        selectedEdges: [],
      });
    },

    openNodeSettings: (nodeId) => {
      set({
        nodeSettingsModal: {
          open: true,
          nodeId,
        },
      });
    },

    // Modal actions
    setNodeSettingsModal: (open, nodeId = null) => {
      set({
        nodeSettingsModal: {
          open,
          nodeId: open ? nodeId : null,
        },
      });
    },
  })),
);

// Expose store to window for debugging (development only)
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  (window as any).useEditorStore = useEditorStore;

  // Debug function to test flow highlighting
  (window as any).testFlowHighlighting = (nodeId: string) => {
    const store = useEditorStore.getState();
    console.log("Testing flow highlighting for node:", nodeId);
    console.log(
      "Current nodes:",
      store.nodes.map((n) => ({
        id: n.id,
        type: n.data?.type,
        label: n.data?.label,
      })),
    );
    console.log(
      "Current edges:",
      store.edges.map((e) => ({
        id: e.id,
        source: e.source,
        target: e.target,
      })),
    );

    const result = buildLinearFlow(nodeId, store.nodes, store.edges);
    console.log("Flow highlighting result (ALL PATHS):", result);
    console.log("Total highlighted nodes:", result.highlightedNodes.length);
    console.log("Total highlighted edges:", result.highlightedEdges.length);

    store.highlightFlow(nodeId);
    console.log("Updated highlighted nodes:", store.highlightedNodes);
    console.log("Updated highlighted edges:", store.highlightedEdges);
  };
}
